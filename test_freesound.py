#!/usr/bin/env python3
"""
Simple test script to check Freesound API directly.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_freesound_direct():
    """Test Freesound API directly."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        from music_sources.freesound import FreesoundClient
        from music_sources.base import SearchQuery
        
        api_key = os.getenv("FREESOUND_API_KEY")
        if not api_key:
            print("❌ No API key found")
            return False
        
        print(f"✅ API Key: {api_key[:10]}...")
        
        # Create client
        client = FreesoundClient(api_key)
        
        # Test simple search
        print("🔍 Testing simple search...")
        query = SearchQuery(query="lofi", limit=5)
        result = await client.search(query)
        
        print(f"Found {len(result.tracks)} tracks")
        print(f"Total count: {result.total_count}")
        
        for i, track in enumerate(result.tracks[:3], 1):
            print(f"  {i}. {track.title} by {track.artist} ({track.duration:.1f}s)")
            print(f"     Tags: {', '.join(track.tags[:5])}")
        
        # Test broader search
        print("\n🔍 Testing broader search...")
        broad_query = SearchQuery(query="chill", limit=10)
        broad_result = await client.search(broad_query)
        
        print(f"Broad search found {len(broad_result.tracks)} tracks")
        print(f"Total count: {broad_result.total_count}")
        
        # Cleanup
        await client.close()
        
        return len(result.tracks) > 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Testing Freesound API directly")
    print("=" * 40)
    
    success = asyncio.run(test_freesound_direct())
    
    if success:
        print("\n✅ Freesound API is working!")
    else:
        print("\n❌ Freesound API test failed")
