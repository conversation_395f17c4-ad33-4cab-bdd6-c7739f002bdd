#!/usr/bin/env python3
"""
Simple test script to check Freesound API directly.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_freesound_direct():
    """Test Freesound API directly."""
    try:
        from dotenv import load_dotenv
        load_dotenv()

        from music_sources.freesound import FreesoundClient
        from music_sources.base import SearchQuery
        from lofi_manager import LoFiMusicManager

        api_key = os.getenv("FREESOUND_API_KEY")
        if not api_key:
            print("❌ No API key found")
            return False

        print(f"✅ API Key: {api_key[:10]}...")

        # Create client
        client = FreesoundClient(api_key)

        # Test simple search with duration filter
        print("🔍 Testing search with duration filter...")
        query = SearchQuery(
            query="lofi",
            min_duration=60.0,  # At least 1 minute
            max_duration=300.0,  # At most 5 minutes
            limit=10
        )
        result = await client.search(query)

        print(f"Found {len(result.tracks)} tracks with duration filter")
        print(f"Total count: {result.total_count}")

        for i, track in enumerate(result.tracks[:5], 1):
            print(f"  {i}. {track.title} by {track.artist} ({track.duration:.1f}s)")
            print(f"     Tags: {', '.join(track.tags[:5])}")

        # Test filtering logic
        print("\n🔍 Testing filtering logic...")
        manager = LoFiMusicManager()

        if result.tracks:
            print("Testing _is_lofi_track filter on first few tracks:")
            for i, track in enumerate(result.tracks[:3], 1):
                is_lofi = manager._is_lofi_track(track, None)  # Pass None for style
                print(f"  Track {i}: {track.title} -> {'✅ PASS' if is_lofi else '❌ FILTERED OUT'}")
                if not is_lofi:
                    print(f"    Duration: {track.duration}s")
                    print(f"    Tags: {track.tags}")

        # Test broader search without filters
        print("\n🔍 Testing broader search without filters...")
        broad_query = SearchQuery(query="chill OR ambient OR beats", limit=10)
        broad_result = await client.search(broad_query)

        print(f"Broad search found {len(broad_result.tracks)} tracks")
        print(f"Total count: {broad_result.total_count}")

        # Cleanup
        await client.close()

        return len(result.tracks) > 0

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Testing Freesound API directly")
    print("=" * 40)
    
    success = asyncio.run(test_freesound_direct())
    
    if success:
        print("\n✅ Freesound API is working!")
    else:
        print("\n❌ Freesound API test failed")
