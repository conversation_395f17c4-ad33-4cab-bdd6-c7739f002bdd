"""
Slack notification service for YouTube upload events.
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

import httpx

logger = logging.getLogger(__name__)


class SlackNotifier:
    """Sends notifications to <PERSON>lack via webhook."""
    
    def __init__(self, webhook_url: Optional[str] = None):
        """
        Initialize Slack notifier.
        
        Args:
            webhook_url: Slack webhook URL
        """
        self.webhook_url = webhook_url
        self.enabled = bool(webhook_url)
    
    async def send_notification(self, message: str, color: str = "good", 
                              fields: Optional[list] = None) -> bool:
        """
        Send a notification to Slack.
        
        Args:
            message: Main message text
            color: Message color (good, warning, danger, or hex color)
            fields: Additional fields to include
            
        Returns:
            True if sent successfully, False otherwise
        """
        if not self.enabled:
            logger.debug("Slack notifications disabled - no webhook URL")
            return False
        
        try:
            payload = {
                "attachments": [{
                    "color": color,
                    "text": message,
                    "ts": int(datetime.now().timestamp())
                }]
            }
            
            if fields:
                payload["attachments"][0]["fields"] = fields
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(self.webhook_url, json=payload)
                
                if response.status_code == 200:
                    logger.debug("✅ Slack notification sent successfully")
                    return True
                else:
                    logger.error(f"❌ Slack notification failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Failed to send Slack notification: {e}")
            return False
    
    async def notify_upload_started(self, video_title: str, video_path: Path) -> bool:
        """Notify that video upload has started."""
        message = f"🚀 *Upload Started*\n{video_title}"
        fields = [
            {"title": "File", "value": str(video_path.name), "short": True},
            {"title": "Status", "value": "Uploading to YouTube", "short": True}
        ]
        return await self.send_notification(message, "good", fields)
    
    async def notify_upload_completed(self, video_title: str, video_id: str, 
                                    video_url: str) -> bool:
        """Notify that video upload has completed."""
        message = f"✅ *Upload Completed*\n{video_title}"
        fields = [
            {"title": "Video ID", "value": video_id, "short": True},
            {"title": "Status", "value": "Draft (Private)", "short": True},
            {"title": "URL", "value": f"<{video_url}|View Video>", "short": False}
        ]
        return await self.send_notification(message, "good", fields)
    
    async def notify_upload_failed(self, video_title: str, error: str, 
                                 video_path: Path) -> bool:
        """Notify that video upload has failed."""
        message = f"❌ *Upload Failed*\n{video_title}"
        fields = [
            {"title": "File", "value": str(video_path.name), "short": True},
            {"title": "Error", "value": error[:100] + "..." if len(error) > 100 else error, "short": False}
        ]
        return await self.send_notification(message, "danger", fields)
    
    async def notify_publishing_scheduled(self, video_title: str, video_id: str,
                                        scheduled_time: datetime) -> bool:
        """Notify that video publishing has been scheduled."""
        message = f"📅 *Publishing Scheduled*\n{video_title}"
        fields = [
            {"title": "Video ID", "value": video_id, "short": True},
            {"title": "Scheduled Time", "value": scheduled_time.strftime("%Y-%m-%d %H:%M %Z"), "short": True}
        ]
        return await self.send_notification(message, "good", fields)
    
    async def notify_published(self, video_title: str, video_id: str, 
                             video_url: str) -> bool:
        """Notify that video has been published."""
        message = f"🎉 *Video Published*\n{video_title}"
        fields = [
            {"title": "Video ID", "value": video_id, "short": True},
            {"title": "Status", "value": "Public", "short": True},
            {"title": "URL", "value": f"<{video_url}|Watch Now>", "short": False}
        ]
        return await self.send_notification(message, "good", fields)
    
    async def notify_publishing_failed(self, video_title: str, video_id: str, 
                                     error: str) -> bool:
        """Notify that video publishing has failed."""
        message = f"⚠️ *Publishing Failed*\n{video_title}"
        fields = [
            {"title": "Video ID", "value": video_id, "short": True},
            {"title": "Error", "value": error[:100] + "..." if len(error) > 100 else error, "short": False}
        ]
        return await self.send_notification(message, "warning", fields)
    
    async def notify_duplicate_detected(self, video_title: str, video_path: Path,
                                      existing_video_id: Optional[str] = None) -> bool:
        """Notify that a duplicate video was detected."""
        message = f"⚠️ *Duplicate Video Detected*\n{video_title}"
        fields = [
            {"title": "File", "value": str(video_path.name), "short": True},
            {"title": "Action", "value": "Upload skipped", "short": True}
        ]
        
        if existing_video_id:
            fields.append({
                "title": "Existing Video", 
                "value": f"<https://www.youtube.com/watch?v={existing_video_id}|{existing_video_id}>", 
                "short": False
            })
        
        return await self.send_notification(message, "warning", fields)
    
    async def notify_content_violation(self, video_title: str, violations: list) -> bool:
        """Notify about content guideline violations."""
        message = f"🚫 *Content Guideline Violations*\n{video_title}"
        fields = [
            {"title": "Violations", "value": "\n".join(f"• {v}" for v in violations), "short": False},
            {"title": "Action", "value": "Upload blocked", "short": True}
        ]
        return await self.send_notification(message, "danger", fields)
    
    async def notify_system_error(self, component: str, error: str, 
                                context: Optional[Dict[str, Any]] = None) -> bool:
        """Notify about system errors."""
        message = f"🔥 *System Error in {component}*"
        fields = [
            {"title": "Error", "value": error[:200] + "..." if len(error) > 200 else error, "short": False}
        ]
        
        if context:
            for key, value in context.items():
                if len(fields) < 10:  # Slack limit
                    fields.append({"title": key.title(), "value": str(value), "short": True})
        
        return await self.send_notification(message, "danger", fields)
    
    async def test_connection(self) -> bool:
        """Test Slack webhook connection."""
        if not self.enabled:
            logger.warning("⚠️ Slack webhook URL not configured")
            return False
        
        message = "🧪 *Test Notification*\nLo-Fi Channel notification system is working!"
        fields = [
            {"title": "Timestamp", "value": datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "short": True},
            {"title": "Status", "value": "Connected", "short": True}
        ]
        
        success = await self.send_notification(message, "good", fields)
        if success:
            logger.info("✅ Slack connection test successful")
        else:
            logger.error("❌ Slack connection test failed")
        
        return success
