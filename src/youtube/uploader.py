"""
YouTube uploader service for lo-fi videos.
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from google.auth.transport.requests import Request
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload

# Always use absolute imports
from youtube.metadata import VideoMetadata

logger = logging.getLogger(__name__)


class YouTubeUploader:
    """Handles YouTube video uploads using service account authentication."""
    
    # YouTube API scopes
    SCOPES = ['https://www.googleapis.com/auth/youtube.upload']
    
    def __init__(self, service_account_file: str, channel_id: Optional[str] = None):
        """
        Initialize YouTube uploader.
        
        Args:
            service_account_file: Path to service account JSON key file
            channel_id: YouTube channel ID (optional, for validation)
        """
        self.service_account_file = Path(service_account_file)
        self.channel_id = channel_id
        self.youtube_service = None
        self._authenticated = False
    
    async def authenticate(self) -> bool:
        """Authenticate with YouTube API using service account."""
        try:
            if not self.service_account_file.exists():
                logger.error(f"❌ Service account file not found: {self.service_account_file}")
                return False
            
            # Load service account credentials
            credentials = service_account.Credentials.from_service_account_file(
                str(self.service_account_file),
                scopes=self.SCOPES
            )
            
            # Build YouTube service
            self.youtube_service = build('youtube', 'v3', credentials=credentials)
            self._authenticated = True
            
            logger.info("✅ YouTube API authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ YouTube authentication failed: {e}")
            return False
    
    async def upload_video(
        self,
        video_path: Path,
        metadata: VideoMetadata,
        notify_subscribers: bool = False
    ) -> Optional[str]:
        """
        Upload video to YouTube as a draft.
        
        Args:
            video_path: Path to video file
            metadata: Video metadata
            notify_subscribers: Whether to notify subscribers (default: False for drafts)
            
        Returns:
            YouTube video ID if successful, None otherwise
        """
        if not self._authenticated:
            if not await self.authenticate():
                return None
        
        if not video_path.exists():
            logger.error(f"❌ Video file not found: {video_path}")
            return None
        
        try:
            # Prepare video metadata for upload
            body = {
                'snippet': {
                    'title': metadata.title,
                    'description': metadata.description,
                    'tags': metadata.tags,
                    'categoryId': metadata.category_id
                },
                'status': {
                    'privacyStatus': metadata.privacy_status,
                    'selfDeclaredMadeForKids': False,
                    'notifySubscribers': notify_subscribers
                }
            }
            
            # Create media upload object
            media = MediaFileUpload(
                str(video_path),
                chunksize=-1,  # Upload in single chunk
                resumable=True,
                mimetype='video/*'
            )
            
            logger.info(f"🚀 Starting upload: {metadata.title}")
            
            # Execute upload
            insert_request = self.youtube_service.videos().insert(
                part=','.join(body.keys()),
                body=body,
                media_body=media
            )
            
            response = None
            error = None
            retry = 0
            max_retries = 3
            
            while response is None and retry < max_retries:
                try:
                    status, response = insert_request.next_chunk()
                    if status:
                        logger.info(f"📤 Upload progress: {int(status.progress() * 100)}%")
                except HttpError as e:
                    if e.resp.status in [500, 502, 503, 504]:
                        # Retriable error
                        retry += 1
                        logger.warning(f"⚠️ Retriable error {e.resp.status}, retry {retry}/{max_retries}")
                        continue
                    else:
                        logger.error(f"❌ Non-retriable HTTP error: {e}")
                        raise
                except Exception as e:
                    logger.error(f"❌ Upload error: {e}")
                    raise
            
            if response is not None:
                video_id = response['id']
                logger.info(f"✅ Video uploaded successfully: {video_id}")
                logger.info(f"🔗 Video URL: https://www.youtube.com/watch?v={video_id}")
                return video_id
            else:
                logger.error("❌ Upload failed after all retries")
                return None
                
        except HttpError as e:
            logger.error(f"❌ YouTube API error: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Upload failed: {e}")
            return None
    
    async def update_video_status(
        self,
        video_id: str,
        privacy_status: str = "public",
        publish_at: Optional[datetime] = None
    ) -> bool:
        """
        Update video privacy status or schedule publication.
        
        Args:
            video_id: YouTube video ID
            privacy_status: New privacy status ('public', 'private', 'unlisted')
            publish_at: Schedule publication time (for 'public' status)
            
        Returns:
            True if successful, False otherwise
        """
        if not self._authenticated:
            if not await self.authenticate():
                return False
        
        try:
            body = {
                'id': video_id,
                'status': {
                    'privacyStatus': privacy_status,
                    'selfDeclaredMadeForKids': False
                }
            }
            
            # Add scheduled publish time if provided
            if publish_at and privacy_status == 'public':
                body['status']['publishAt'] = publish_at.isoformat() + 'Z'
            
            response = self.youtube_service.videos().update(
                part='status',
                body=body
            ).execute()
            
            logger.info(f"✅ Video {video_id} status updated to {privacy_status}")
            return True
            
        except HttpError as e:
            logger.error(f"❌ Failed to update video status: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Error updating video status: {e}")
            return False
    
    async def get_video_info(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get video information from YouTube."""
        if not self._authenticated:
            if not await self.authenticate():
                return None
        
        try:
            response = self.youtube_service.videos().list(
                part='snippet,status,statistics',
                id=video_id
            ).execute()
            
            if response['items']:
                return response['items'][0]
            else:
                logger.warning(f"⚠️ Video not found: {video_id}")
                return None
                
        except HttpError as e:
            logger.error(f"❌ Failed to get video info: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error getting video info: {e}")
            return None
    
    async def delete_video(self, video_id: str) -> bool:
        """Delete a video from YouTube."""
        if not self._authenticated:
            if not await self.authenticate():
                return False
        
        try:
            self.youtube_service.videos().delete(id=video_id).execute()
            logger.info(f"✅ Video deleted: {video_id}")
            return True
            
        except HttpError as e:
            logger.error(f"❌ Failed to delete video: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Error deleting video: {e}")
            return False
    
    def validate_setup(self) -> Dict[str, bool]:
        """Validate YouTube uploader setup."""
        checks = {
            "service_account_file_exists": self.service_account_file.exists(),
            "service_account_file_readable": False,
            "credentials_valid": False
        }
        
        if checks["service_account_file_exists"]:
            try:
                with open(self.service_account_file, 'r') as f:
                    import json
                    data = json.load(f)
                    checks["service_account_file_readable"] = True
                    
                    # Check if it has required fields
                    required_fields = ['type', 'project_id', 'private_key', 'client_email']
                    if all(field in data for field in required_fields):
                        checks["credentials_valid"] = data.get('type') == 'service_account'
                        
            except Exception as e:
                logger.error(f"Error validating service account file: {e}")
        
        return checks
