"""
YouTube video metadata generation for lo-fi videos.
"""

import random
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
from pathlib import Path

# Always use absolute imports
from music_sources.base import Track, LoFiStyle


@dataclass
class VideoMetadata:
    """YouTube video metadata."""
    title: str
    description: str
    tags: List[str]
    category_id: str = "10"  # Music category
    privacy_status: str = "private"  # Start as private/draft
    thumbnail_path: Optional[str] = None


def generate_video_metadata(
    style: LoFiStyle,
    tracks: List[Track],
    duration: float,
    video_path: Path
) -> VideoMetadata:
    """Generate YouTube metadata for a lo-fi video."""
    
    # Random activity words for title
    activities = [
        "Study", "Chill", "Relax", "Focus", "Work", "Sleep", "Meditation",
        "Reading", "Coding", "Writing", "Thinking", "Dreaming"
    ]
    
    activity = random.choice(activities)
    
    # Generate title
    title = f"{style.value.title()} Beats for {activity} | Lo-Fi Hip Hop Mix"
    
    # Generate description with attribution
    description_parts = [
        f"🎵 {style.value.title()} lo-fi beats perfect for {activity.lower()}",
        "",
        "✨ Tracklist:",
    ]
    
    # Add track attributions
    for i, track in enumerate(tracks, 1):
        attribution = f"{i}. {track.title}"
        if track.artist:
            attribution += f" by {track.artist}"
        if track.source:
            attribution += f" (via {track.source})"
        if track.license_type:
            attribution += f" - {track.license_type.value}"
        description_parts.append(attribution)
    
    description_parts.extend([
        "",
        "🎧 Perfect for:",
        f"• {activity}",
        "• Background music",
        "• Concentration",
        "• Relaxation",
        "",
        "📝 All tracks are royalty-free and properly licensed.",
        "🔔 Subscribe for more lo-fi beats!",
        "",
        f"⏱️ Duration: {int(duration // 60)}:{int(duration % 60):02d}",
        f"🎨 Style: {style.value.title()}",
        "",
        "#lofi #chillhop #studymusic #focusmusic #relaxing #instrumental"
    ])
    
    description = "\n".join(description_parts)
    
    # Generate tags
    base_tags = [
        "lofi", "lo-fi", "chill", "hip hop", "beats", "instrumental",
        "study music", "focus music", "relaxing music", "background music",
        style.value, activity.lower()
    ]
    
    # Add style-specific tags
    style_tags = {
        LoFiStyle.UPBEAT: ["upbeat", "energetic", "positive"],
        LoFiStyle.CALMING: ["calm", "peaceful", "soothing"],
        LoFiStyle.CHILL: ["chill", "laid back", "mellow"],
        LoFiStyle.DREAMY: ["dreamy", "ethereal", "ambient"],
        LoFiStyle.NOSTALGIC: ["nostalgic", "vintage", "retro"],
        LoFiStyle.FOCUS: ["focus", "concentration", "productivity"],
        LoFiStyle.STUDY: ["study", "homework", "learning"],
        LoFiStyle.RELAXING: ["relaxing", "stress relief", "meditation"],
        LoFiStyle.AMBIENT: ["ambient", "atmospheric", "soundscape"],
        LoFiStyle.JAZZY: ["jazz", "jazzy", "smooth"]
    }
    
    if style in style_tags:
        base_tags.extend(style_tags[style])
    
    # Remove duplicates and limit to YouTube's tag limit
    tags = list(dict.fromkeys(base_tags))[:15]  # YouTube allows up to 500 chars total
    
    return VideoMetadata(
        title=title,
        description=description,
        tags=tags,
        privacy_status="private"  # Upload as draft
    )


def generate_thumbnail_text(style: LoFiStyle, activity: str) -> str:
    """Generate text for auto-generated thumbnail."""
    return f"{style.value.upper()}\nBEATS\nFOR {activity.upper()}"


def validate_content_guidelines(metadata: VideoMetadata, guidelines: dict) -> List[str]:
    """Validate video metadata against content guidelines."""
    violations = []
    
    # Check title length
    max_title_length = int(guidelines.get("max_title_length", 100))
    if len(metadata.title) > max_title_length:
        violations.append(f"Title too long: {len(metadata.title)} > {max_title_length}")
    
    # Check description length
    max_desc_length = int(guidelines.get("max_description_length", 5000))
    if len(metadata.description) > max_desc_length:
        violations.append(f"Description too long: {len(metadata.description)} > {max_desc_length}")
    
    # Check for prohibited words
    prohibited_words = guidelines.get("prohibited_words", "").split(",")
    prohibited_words = [word.strip().lower() for word in prohibited_words if word.strip()]
    
    title_lower = metadata.title.lower()
    desc_lower = metadata.description.lower()
    
    for word in prohibited_words:
        if word in title_lower:
            violations.append(f"Prohibited word in title: '{word}'")
        if word in desc_lower:
            violations.append(f"Prohibited word in description: '{word}'")
    
    # Check required attribution
    require_attribution = guidelines.get("require_attribution", "true").lower() == "true"
    if require_attribution:
        if "license" not in desc_lower and "attribution" not in desc_lower:
            violations.append("Missing license/attribution information")
    
    return violations
