"""
Database models for YouTube upload tracking.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from enum import Enum


class UploadStatus(Enum):
    """Status of video upload."""
    PENDING = "pending"
    UPLOADING = "uploading"
    UPLOADED = "uploaded"
    SCHEDULED = "scheduled"
    PUBLISHED = "published"
    FAILED = "failed"


@dataclass
class UploadedVideo:
    """Model for tracking uploaded videos."""
    id: Optional[int] = None
    video_path: str = ""
    file_hash: str = ""
    youtube_video_id: Optional[str] = None
    title: str = ""
    description: str = ""
    style: str = ""
    duration: int = 0  # in seconds
    upload_status: UploadStatus = UploadStatus.PENDING
    scheduled_publish_time: Optional[datetime] = None
    actual_publish_time: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> dict:
        """Convert to dictionary for database storage."""
        return {
            'id': self.id,
            'video_path': self.video_path,
            'file_hash': self.file_hash,
            'youtube_video_id': self.youtube_video_id,
            'title': self.title,
            'description': self.description,
            'style': self.style,
            'duration': self.duration,
            'upload_status': self.upload_status.value,
            'scheduled_publish_time': self.scheduled_publish_time.isoformat() if self.scheduled_publish_time else None,
            'actual_publish_time': self.actual_publish_time.isoformat() if self.actual_publish_time else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'UploadedVideo':
        """Create instance from dictionary."""
        return cls(
            id=data.get('id'),
            video_path=data.get('video_path', ''),
            file_hash=data.get('file_hash', ''),
            youtube_video_id=data.get('youtube_video_id'),
            title=data.get('title', ''),
            description=data.get('description', ''),
            style=data.get('style', ''),
            duration=data.get('duration', 0),
            upload_status=UploadStatus(data.get('upload_status', 'pending')),
            scheduled_publish_time=datetime.fromisoformat(data['scheduled_publish_time']) if data.get('scheduled_publish_time') else None,
            actual_publish_time=datetime.fromisoformat(data['actual_publish_time']) if data.get('actual_publish_time') else None,
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
            updated_at=datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else None,
        )


@dataclass
class ContentGuideline:
    """Model for content guidelines."""
    id: Optional[int] = None
    rule_name: str = ""
    rule_value: str = ""
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> dict:
        """Convert to dictionary for database storage."""
        return {
            'id': self.id,
            'rule_name': self.rule_name,
            'rule_value': self.rule_value,
            'created_at': self.created_at.isoformat() if self.created_at else None,
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ContentGuideline':
        """Create instance from dictionary."""
        return cls(
            id=data.get('id'),
            rule_name=data.get('rule_name', ''),
            rule_value=data.get('rule_value', ''),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
        )
