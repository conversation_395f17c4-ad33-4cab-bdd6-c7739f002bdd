"""
Database manager for YouTube upload tracking and content guidelines.
"""

import aiosqlite
import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
# Always use absolute imports
from database.models import UploadedVideo, ContentGuideline, UploadStatus

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages SQLite database operations for upload tracking."""
    
    def __init__(self, db_path: str = "lofi_channel.db"):
        self.db_path = Path(db_path)
        self._initialized = False
    
    async def initialize(self):
        """Initialize database and create tables if they don't exist."""
        if self._initialized:
            return
            
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await self._create_tables(db)
                await db.commit()
            self._initialized = True
            logger.info(f"✅ Database initialized: {self.db_path}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize database: {e}")
            raise
    
    async def _create_tables(self, db: aiosqlite.Connection):
        """Create database tables."""
        
        # Uploaded videos table
        await db.execute("""
            CREATE TABLE IF NOT EXISTS uploaded_videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_path TEXT NOT NULL,
                file_hash TEXT UNIQUE NOT NULL,
                youtube_video_id TEXT,
                title TEXT NOT NULL,
                description TEXT,
                style TEXT NOT NULL,
                duration INTEGER NOT NULL,
                upload_status TEXT NOT NULL DEFAULT 'pending',
                scheduled_publish_time TEXT,
                actual_publish_time TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)
        
        # Content guidelines table
        await db.execute("""
            CREATE TABLE IF NOT EXISTS content_guidelines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rule_name TEXT UNIQUE NOT NULL,
                rule_value TEXT NOT NULL,
                created_at TEXT NOT NULL
            )
        """)
        
        # Create indexes for better performance
        await db.execute("CREATE INDEX IF NOT EXISTS idx_file_hash ON uploaded_videos(file_hash)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_upload_status ON uploaded_videos(upload_status)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_scheduled_publish ON uploaded_videos(scheduled_publish_time)")
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA256 hash of a file for duplicate detection."""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating file hash for {file_path}: {e}")
            raise
    
    async def add_video(self, video: UploadedVideo) -> int:
        """Add a new video record to the database."""
        await self.initialize()
        
        now = datetime.now().isoformat()
        video.created_at = datetime.fromisoformat(now)
        video.updated_at = datetime.fromisoformat(now)
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    INSERT INTO uploaded_videos (
                        video_path, file_hash, youtube_video_id, title, description,
                        style, duration, upload_status, scheduled_publish_time,
                        actual_publish_time, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    video.video_path, video.file_hash, video.youtube_video_id,
                    video.title, video.description, video.style, video.duration,
                    video.upload_status.value,
                    video.scheduled_publish_time.isoformat() if video.scheduled_publish_time else None,
                    video.actual_publish_time.isoformat() if video.actual_publish_time else None,
                    now, now
                ))
                await db.commit()
                video_id = cursor.lastrowid
                logger.info(f"✅ Added video record: {video.title} (ID: {video_id})")
                return video_id
        except aiosqlite.IntegrityError as e:
            if "file_hash" in str(e):
                logger.warning(f"⚠️ Duplicate video detected: {video.video_path}")
                raise ValueError(f"Video already exists in database: {video.video_path}")
            raise
        except Exception as e:
            logger.error(f"❌ Failed to add video record: {e}")
            raise
    
    async def update_video_status(self, video_id: int, status: UploadStatus, 
                                youtube_video_id: Optional[str] = None) -> bool:
        """Update video upload status."""
        await self.initialize()
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                now = datetime.now().isoformat()
                
                if youtube_video_id:
                    await db.execute("""
                        UPDATE uploaded_videos 
                        SET upload_status = ?, youtube_video_id = ?, updated_at = ?
                        WHERE id = ?
                    """, (status.value, youtube_video_id, now, video_id))
                else:
                    await db.execute("""
                        UPDATE uploaded_videos 
                        SET upload_status = ?, updated_at = ?
                        WHERE id = ?
                    """, (status.value, now, video_id))
                
                await db.commit()
                
                if db.total_changes > 0:
                    logger.info(f"✅ Updated video {video_id} status to {status.value}")
                    return True
                else:
                    logger.warning(f"⚠️ No video found with ID {video_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Failed to update video status: {e}")
            raise
    
    async def mark_published(self, video_id: int) -> bool:
        """Mark video as published with current timestamp."""
        await self.initialize()
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                now = datetime.now().isoformat()
                
                await db.execute("""
                    UPDATE uploaded_videos 
                    SET upload_status = ?, actual_publish_time = ?, updated_at = ?
                    WHERE id = ?
                """, (UploadStatus.PUBLISHED.value, now, now, video_id))
                
                await db.commit()
                
                if db.total_changes > 0:
                    logger.info(f"✅ Marked video {video_id} as published")
                    return True
                else:
                    logger.warning(f"⚠️ No video found with ID {video_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Failed to mark video as published: {e}")
            raise
    
    async def get_video_by_hash(self, file_hash: str) -> Optional[UploadedVideo]:
        """Get video record by file hash."""
        await self.initialize()
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute("""
                    SELECT * FROM uploaded_videos WHERE file_hash = ?
                """, (file_hash,))
                row = await cursor.fetchone()
                
                if row:
                    return UploadedVideo.from_dict(dict(row))
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get video by hash: {e}")
            raise
    
    async def get_scheduled_videos(self, before_time: datetime) -> List[UploadedVideo]:
        """Get videos scheduled for publishing before the given time."""
        await self.initialize()
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute("""
                    SELECT * FROM uploaded_videos 
                    WHERE upload_status = ? 
                    AND scheduled_publish_time IS NOT NULL 
                    AND scheduled_publish_time <= ?
                """, (UploadStatus.SCHEDULED.value, before_time.isoformat()))
                
                rows = await cursor.fetchall()
                return [UploadedVideo.from_dict(dict(row)) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Failed to get scheduled videos: {e}")
            raise
    
    async def get_videos_by_status(self, status: UploadStatus) -> List[UploadedVideo]:
        """Get all videos with a specific status."""
        await self.initialize()
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute("""
                    SELECT * FROM uploaded_videos WHERE upload_status = ?
                    ORDER BY created_at DESC
                """, (status.value,))
                
                rows = await cursor.fetchall()
                return [UploadedVideo.from_dict(dict(row)) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Failed to get videos by status: {e}")
            raise
    
    async def add_content_guideline(self, guideline: ContentGuideline) -> int:
        """Add a content guideline."""
        await self.initialize()
        
        now = datetime.now().isoformat()
        guideline.created_at = datetime.fromisoformat(now)
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    INSERT OR REPLACE INTO content_guidelines (rule_name, rule_value, created_at)
                    VALUES (?, ?, ?)
                """, (guideline.rule_name, guideline.rule_value, now))
                await db.commit()
                return cursor.lastrowid
                
        except Exception as e:
            logger.error(f"❌ Failed to add content guideline: {e}")
            raise
    
    async def get_content_guidelines(self) -> Dict[str, str]:
        """Get all content guidelines as a dictionary."""
        await self.initialize()
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute("SELECT rule_name, rule_value FROM content_guidelines")
                rows = await cursor.fetchall()
                return {row['rule_name']: row['rule_value'] for row in rows}
                
        except Exception as e:
            logger.error(f"❌ Failed to get content guidelines: {e}")
            raise
    
    async def close(self):
        """Close database connections (cleanup method)."""
        # aiosqlite doesn't maintain persistent connections, so nothing to close
        pass
