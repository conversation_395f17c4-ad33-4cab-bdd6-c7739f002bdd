"""
Error tracking integration using GitLab integrated error tracking (Sentry-compatible).
"""

import os
import logging
from typing import Optional, Dict, Any
from functools import wraps

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.asyncio import AsyncioIntegration

logger = logging.getLogger(__name__)


def initialize_error_tracking(
    dsn: Optional[str] = None,
    environment: str = "production",
    release: Optional[str] = None,
    sample_rate: float = 1.0,
    debug: bool = False
) -> bool:
    """
    Initialize GitLab integrated error tracking.
    
    Args:
        dsn: GitLab error tracking DSN
        environment: Environment name (production, staging, development)
        release: Release version
        sample_rate: Error sampling rate (0.0 to 1.0)
        debug: Enable debug mode
        
    Returns:
        True if initialized successfully, False otherwise
    """
    if not dsn:
        dsn = os.getenv("SENTRY_DSN")
    
    if not dsn:
        logger.warning("⚠️ Error tracking DSN not configured")
        return False
    
    try:
        # Configure logging integration
        logging_integration = LoggingIntegration(
            level=logging.INFO,        # Capture info and above as breadcrumbs
            event_level=logging.ERROR  # Send errors as events
        )
        
        # Configure asyncio integration
        asyncio_integration = AsyncioIntegration()
        
        # Initialize Sentry SDK
        sentry_sdk.init(
            dsn=dsn,
            environment=environment,
            release=release,
            sample_rate=sample_rate,
            debug=debug,
            integrations=[
                logging_integration,
                asyncio_integration,
            ],
            # Set additional options
            attach_stacktrace=True,
            send_default_pii=False,  # Don't send personally identifiable information
            max_breadcrumbs=50,
            before_send=_before_send_filter,
        )
        
        # Set user context
        sentry_sdk.set_user({
            "id": "lofi-channel-system",
            "username": "system"
        })
        
        # Set tags
        sentry_sdk.set_tag("component", "lofi-channel")
        sentry_sdk.set_tag("service", "youtube-uploader")
        
        logger.info("✅ Error tracking initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize error tracking: {e}")
        return False


def _before_send_filter(event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Filter events before sending to error tracking.
    
    Args:
        event: Sentry event data
        hint: Additional context
        
    Returns:
        Modified event or None to drop the event
    """
    # Don't send certain types of errors
    if 'exc_info' in hint:
        exc_type, exc_value, tb = hint['exc_info']
        
        # Skip certain exception types
        if exc_type.__name__ in ['KeyboardInterrupt', 'SystemExit']:
            return None
        
        # Skip HTTP client errors (4xx) but keep server errors (5xx)
        if hasattr(exc_value, 'response') and hasattr(exc_value.response, 'status_code'):
            status_code = exc_value.response.status_code
            if 400 <= status_code < 500:
                return None
    
    # Add custom context
    event.setdefault('tags', {})
    event['tags']['service'] = 'lofi-channel'
    
    return event


def capture_exception(error: Exception, 
                     context: Optional[Dict[str, Any]] = None,
                     tags: Optional[Dict[str, str]] = None,
                     level: str = "error") -> str:
    """
    Capture an exception with additional context.
    
    Args:
        error: Exception to capture
        context: Additional context data
        tags: Additional tags
        level: Error level (error, warning, info)
        
    Returns:
        Event ID from error tracking system
    """
    with sentry_sdk.push_scope() as scope:
        # Set level
        scope.level = level
        
        # Add context
        if context:
            for key, value in context.items():
                scope.set_context(key, value)
        
        # Add tags
        if tags:
            for key, value in tags.items():
                scope.set_tag(key, value)
        
        # Capture exception
        event_id = sentry_sdk.capture_exception(error)
        
        logger.debug(f"Exception captured with ID: {event_id}")
        return event_id


def capture_message(message: str,
                   level: str = "info",
                   context: Optional[Dict[str, Any]] = None,
                   tags: Optional[Dict[str, str]] = None) -> str:
    """
    Capture a message with additional context.
    
    Args:
        message: Message to capture
        level: Message level (error, warning, info, debug)
        context: Additional context data
        tags: Additional tags
        
    Returns:
        Event ID from error tracking system
    """
    with sentry_sdk.push_scope() as scope:
        # Set level
        scope.level = level
        
        # Add context
        if context:
            for key, value in context.items():
                scope.set_context(key, value)
        
        # Add tags
        if tags:
            for key, value in tags.items():
                scope.set_tag(key, value)
        
        # Capture message
        event_id = sentry_sdk.capture_message(message, level)
        
        logger.debug(f"Message captured with ID: {event_id}")
        return event_id


def track_performance(operation_name: str):
    """
    Decorator to track performance of operations.
    
    Args:
        operation_name: Name of the operation being tracked
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(op="task", name=operation_name):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    capture_exception(e, context={
                        "operation": operation_name,
                        "args": str(args)[:200],
                        "kwargs": str(kwargs)[:200]
                    })
                    raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(op="task", name=operation_name):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    capture_exception(e, context={
                        "operation": operation_name,
                        "args": str(args)[:200],
                        "kwargs": str(kwargs)[:200]
                    })
                    raise
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def set_user_context(user_id: str, username: Optional[str] = None, 
                    email: Optional[str] = None) -> None:
    """
    Set user context for error tracking.
    
    Args:
        user_id: User identifier
        username: Username (optional)
        email: Email address (optional)
    """
    user_data = {"id": user_id}
    if username:
        user_data["username"] = username
    if email:
        user_data["email"] = email
    
    sentry_sdk.set_user(user_data)


def set_operation_context(operation: str, video_id: Optional[str] = None,
                         video_title: Optional[str] = None) -> None:
    """
    Set operation context for error tracking.
    
    Args:
        operation: Operation being performed
        video_id: YouTube video ID (optional)
        video_title: Video title (optional)
    """
    context = {"operation": operation}
    if video_id:
        context["video_id"] = video_id
    if video_title:
        context["video_title"] = video_title
    
    sentry_sdk.set_context("operation", context)


def add_breadcrumb(message: str, category: str = "default", 
                  level: str = "info", data: Optional[Dict[str, Any]] = None) -> None:
    """
    Add a breadcrumb for debugging.
    
    Args:
        message: Breadcrumb message
        category: Breadcrumb category
        level: Breadcrumb level
        data: Additional data
    """
    sentry_sdk.add_breadcrumb(
        message=message,
        category=category,
        level=level,
        data=data or {}
    )


def flush_events(timeout: float = 2.0) -> bool:
    """
    Flush pending events to error tracking system.
    
    Args:
        timeout: Maximum time to wait for flush
        
    Returns:
        True if flushed successfully, False otherwise
    """
    try:
        return sentry_sdk.flush(timeout=timeout)
    except Exception as e:
        logger.error(f"❌ Failed to flush error tracking events: {e}")
        return False
