[project]
name = "lofi-channel"
version = "0.1.0"
description = "A lo-fi music channel application with royalty-free music sources"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Core async HTTP and file operations
    "aiohttp>=3.10.0",
    "aiofiles>=24.0.0",
    "httpx>=0.28.0",

    # Configuration and environment
    "python-dotenv>=1.0.0",
    "pydantic>=2.10.0",
    "pydantic-settings>=2.7.0",

    # CLI framework
    "typer>=0.15.0",
    "rich>=13.9.0",

    # Google APIs
    "google-auth>=2.36.0",
    "google-auth-oauthlib>=1.2.0",
    "google-auth-httplib2>=0.2.0",
    "google-api-python-client>=2.154.0",

    # Database
    "aiosqlite>=0.20.0",
    "sqlalchemy[asyncio]>=2.0.36",

    # Scheduling (modern async scheduler)
    "apscheduler>=3.10.0",

    # Image processing
    "pillow>=11.0.0",

    # Error tracking
    "sentry-sdk[fastapi]>=2.19.0",

    # Utilities
    "structlog>=24.4.0",
]

[project.scripts]
lofi = "lofi_cli:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0",
    "sphinx-autodoc-typehints>=1.19.0",
    "sphinx-copybutton>=0.5.0",
]

[tool.pixi.project]
channels = ["conda-forge"]
platforms = ["linux-64"]

# [tool.pixi.pypi-dependencies]
# lofi-channel = { path = ".", editable = true }

[tool.pixi.environments]
default = { solve-group = "default" }
dev = { features = ["dev"], solve-group = "default" }

[tool.pixi.tasks]

[tool.pixi.dependencies]
ffmpeg = ">=6.0.0"
