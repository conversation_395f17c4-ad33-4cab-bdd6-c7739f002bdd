#!/usr/bin/env python3
"""
Modern Lo-Fi Video Generator CLI

Entry point for the modernized CLI using Typer and Rich.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Main entry point for modern CLI."""
    try:
        from cli.main import app
        app()
    except ImportError as e:
        print("❌ Failed to import modern CLI. Please install dependencies:")
        print("pip install typer rich pydantic pydantic-settings apscheduler")
        print(f"Error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
