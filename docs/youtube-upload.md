# YouTube Upload Pipeline

The Lo-Fi Channel includes a comprehensive YouTube upload pipeline that allows you to:

- Upload videos as private drafts
- Schedule videos for automatic publishing
- Track upload status and history
- Receive Slack notifications for all events
- Prevent duplicate uploads
- Validate content against guidelines

## Quick Start

### 1. Setup YouTube API

Run the setup script to configure YouTube Data API v3:

```bash
python scripts/setup_youtube.py
```

This will guide you through:
- Creating a Google Cloud project
- Enabling YouTube Data API v3
- Creating service account credentials
- Setting up channel permissions

### 2. Configure Environment

Add these variables to your `.env` file:

```bash
# Required
YOUTUBE_SERVICE_ACCOUNT_FILE=path/to/service-account-key.json

# Optional
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SENTRY_DSN=https://<EMAIL>/api/v4/error_tracking/collector/your-project-id
TIMEZONE=America/New_York
DEFAULT_PUBLISH_TIME=22:00
```

### 3. Upload a Video

```bash
# Upload as draft
python lofi_cli.py upload video.mp4 chill

# Upload and schedule for 10 PM today
python lofi_cli.py upload video.mp4 chill -t 22:00

# Upload and schedule for specific date/time
python lofi_cli.py upload video.mp4 chill -t 22:00 -d 2024-12-25
```

### 4. Manage Videos

```bash
# Check upload status
python lofi_cli.py status

# Publish a video immediately
python lofi_cli.py publish 5  # Database ID from status command
```

## Scheduling System

### Automatic Publishing

Set up a cron job to automatically publish scheduled videos:

```bash
# Edit crontab
crontab -e

# Add this line to check every 5 minutes
*/5 * * * * cd /path/to/lofi-channel && python scripts/publish_scheduler.py
```

### Scheduling Options

1. **Default Time**: Videos uploaded without scheduling remain as drafts
2. **Same Day**: Use `-t HH:MM` to schedule for today
3. **Future Date**: Use `-t HH:MM -d YYYY-MM-DD` for specific dates
4. **Timezone**: Configured via `TIMEZONE` environment variable

## Video Metadata

### Auto-Generated Content

The system automatically generates:

- **Title**: `[Style] Beats for [Activity] | Lo-Fi Hip Hop Mix`
- **Description**: Includes track attribution, tags, and channel info
- **Tags**: Style-specific and general lo-fi tags
- **Thumbnail**: Auto-generated (future feature)

### Example Generated Metadata

```
Title: Chill Beats for Study | Lo-Fi Hip Hop Mix

Description:
🎵 Chill lo-fi beats perfect for study

✨ Tracklist:
1. Peaceful Morning by Lo-Fi Artist (via Freesound) - CC0
2. Study Session by Chill Producer (via Freesound) - CC BY

🎧 Perfect for:
• Study
• Background music
• Concentration
• Relaxation

📝 All tracks are royalty-free and properly licensed.
🔔 Subscribe for more lo-fi beats!

⏱️ Duration: 3:00
🎨 Style: Chill

#lofi #chillhop #studymusic #focusmusic #relaxing #instrumental
```

## Notifications

### Slack Integration

Configure Slack webhook to receive notifications for:

- ✅ Upload started/completed/failed
- 📅 Publishing scheduled
- 🎉 Video published
- ⚠️ Duplicate detected
- 🚫 Content violations
- 🔥 System errors

### Example Slack Message

```
✅ Upload Completed
Chill Beats for Study | Lo-Fi Hip Hop Mix

Video ID: dQw4w9WgXcQ
Status: Draft (Private)
URL: View Video
```

## Error Tracking

### GitLab Integration

The system integrates with GitLab's error tracking (Sentry-compatible):

1. Enable error tracking in your GitLab project
2. Copy the DSN from GitLab settings
3. Set `SENTRY_DSN` environment variable

### Tracked Events

- Upload failures with context
- API errors and rate limits
- Database issues
- Scheduling problems
- Content guideline violations

## Database Tracking

### Video Records

All uploads are tracked in `lofi_channel.db` with:

- File hash (for duplicate detection)
- YouTube video ID
- Upload status and timestamps
- Scheduled publish time
- Video metadata

### Status Types

- `pending`: Initial state
- `uploading`: Upload in progress
- `uploaded`: Successfully uploaded as draft
- `scheduled`: Scheduled for publishing
- `published`: Live on YouTube
- `failed`: Upload failed

### Query Examples

```bash
# Check all uploaded videos
sqlite3 lofi_channel.db "SELECT title, upload_status, youtube_video_id FROM uploaded_videos;"

# Find scheduled videos
sqlite3 lofi_channel.db "SELECT title, scheduled_publish_time FROM uploaded_videos WHERE upload_status='scheduled';"
```

## Content Guidelines

### Validation Rules

Configure content guidelines in the database:

```python
# Example guidelines
guidelines = {
    "max_title_length": "100",
    "max_description_length": "5000",
    "prohibited_words": "spam,fake,clickbait",
    "require_attribution": "true"
}
```

### Violation Handling

Videos that violate guidelines are:
- Blocked from upload
- Logged to error tracking
- Reported via Slack notifications

## Duplicate Prevention

### File Hash Detection

The system calculates SHA256 hashes of video files to prevent duplicates:

1. Hash calculated before upload
2. Checked against database
3. Upload skipped if duplicate found
4. Notification sent with existing video link

## API Rate Limits

### YouTube Quotas

- Daily quota: 10,000 units
- Upload cost: ~1,600 units per video
- Status update: ~50 units per operation

### Best Practices

- Upload during off-peak hours
- Batch operations when possible
- Monitor quota usage in Google Cloud Console

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check service account file path
   - Verify service account has channel access
   - Ensure YouTube Data API v3 is enabled

2. **Upload Stuck**
   - Check internet connection
   - Verify video file isn't corrupted
   - Check YouTube quota limits

3. **Scheduling Not Working**
   - Verify cron job is running
   - Check timezone configuration
   - Review scheduler logs

### Debug Commands

```bash
# Test YouTube connection
python scripts/setup_youtube.py

# Check upload pipeline status
python lofi_cli.py status

# View scheduler logs
tail -f scheduler.log

# Test Slack notifications
python -c "
from src.notifications.slack import SlackNotifier
import asyncio
notifier = SlackNotifier('YOUR_WEBHOOK_URL')
asyncio.run(notifier.test_connection())
"
```

## Security Considerations

### Service Account Security

- Store JSON key file securely
- Limit service account permissions
- Rotate keys periodically
- Don't commit keys to version control

### Environment Variables

- Use `.env` file for local development
- Use secure secret management in production
- Validate all inputs before processing

## Performance Optimization

### Upload Optimization

- Use appropriate video encoding settings
- Compress videos before upload
- Upload during off-peak hours
- Monitor upload success rates

### Database Optimization

- Regular database maintenance
- Index optimization for queries
- Cleanup old records periodically

## Future Enhancements

### Planned Features

- Custom thumbnail generation
- Playlist management
- Analytics integration
- Bulk upload operations
- Advanced scheduling rules
- Content A/B testing
