# Lo-Fi Channel Configuration
# Copy this file to .env and fill in your API keys

# Freesound.org API Key
# Get yours at: https://freesound.org/apiv2/apply
FREESOUND_API_KEY=

# Pixabay API Key (optional)
# Get yours at: https://pixabay.com/api/docs/
PIXABAY_API_KEY=your_pixabay_api_key_here

# Mubert API Key (optional, for AI-generated music)
# Contact Mubert for API access
MUBERT_API_KEY=your_mubert_api_key_here

# Cache settings
CACHE_DIR=music_cache
MAX_CACHE_SIZE_MB=1000

# Application settings
DEFAULT_SEARCH_LIMIT=20
ENABLE_COMMERCIAL_FILTER=true
REQUIRE_ATTRIBUTION=true

# YouTube Upload Configuration
YOUTUBE_SERVICE_ACCOUNT_FILE=path/to/service-account-key.json
YOUTUBE_CHANNEL_ID=your_youtube_channel_id_here

# Scheduling Configuration
TIMEZONE=UTC
DEFAULT_PUBLISH_TIME=22:00

# Slack Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# GitLab Error Tracking (Sentry-compatible DSN)
SENTRY_DSN=https://<EMAIL>/api/v4/error_tracking/collector/your-project-id

# Database Configuration
DATABASE_PATH=lofi_channel.db