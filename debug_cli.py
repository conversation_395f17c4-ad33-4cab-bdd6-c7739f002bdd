#!/usr/bin/env python3
"""
Debug script to test CLI imports and basic functionality.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_environment():
    """Test environment variable loading."""
    print("Testing environment...")

    try:
        # Test dotenv import
        from dotenv import load_dotenv
        print("✅ python-dotenv imported")

        # Load .env file
        env_file = Path(__file__).parent / ".env"
        print(f"Loading .env from: {env_file}")
        print(f".env exists: {env_file.exists()}")

        if env_file.exists():
            load_dotenv(env_file)
            print("✅ .env file loaded")
        else:
            print("❌ .env file not found")
            return False

        # Check API key
        api_key = os.getenv("FREESOUND_API_KEY")
        if api_key:
            print(f"✅ FREESOUND_API_KEY loaded: {api_key[:10]}...")
            return True
        else:
            print("❌ FREESOUND_API_KEY not found after loading .env")
            return False

    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Test all imports step by step."""
    print("\nTesting imports...")

    try:
        print("1. Testing basic imports...")
        import typer
        from rich.console import Console
        print("✅ Typer and Rich imported")

        print("2. Testing music sources...")
        from music_sources.base import LoFiStyle, Track, LicenseType
        print("✅ Music sources imported")

        print("3. Testing lofi manager...")
        from lofi_manager import LoFiMusicManager
        print("✅ LoFi manager imported")

        print("4. Testing video generator...")
        from video_generator import LoFiVideoGenerator
        print("✅ Video generator imported")

        print("5. Testing error tracking...")
        from error_tracking import initialize_error_tracking
        print("✅ Error tracking imported")

        print("6. Testing upload pipeline...")
        from upload_pipeline import UploadPipeline
        print("✅ Upload pipeline imported")

        print("7. Testing CLI main...")
        from cli.main import app
        print("✅ CLI main imported")

        print("✅ All imports successful!")
        return True

    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_connection():
    """Test API connection and track search."""
    print("\nTesting API connection...")

    try:
        from lofi_manager import LoFiMusicManager
        from music_sources.base import LoFiStyle, SearchQuery

        # Initialize manager
        manager = LoFiMusicManager()

        # Get API key
        api_key = os.getenv("FREESOUND_API_KEY")
        if not api_key:
            print("❌ No API key available for testing")
            return False

        # Configure sources
        manager.configure_sources(api_key)
        print("✅ Manager configured with API key")

        # Test basic search first
        print("🔍 Testing basic search...")
        basic_query = SearchQuery(
            query="lofi",
            limit=5
        )
        basic_results = await manager.manager.search_all_sources(basic_query)
        basic_track_count = sum(len(result.tracks) for result in basic_results.values())
        print(f"Basic search found {basic_track_count} tracks")

        # Test broader search
        print("🔍 Testing broader search...")
        broad_query = SearchQuery(
            query="chill OR ambient OR beats",
            limit=10
        )
        broad_results = await manager.manager.search_all_sources(broad_query)
        broad_track_count = sum(len(result.tracks) for result in broad_results.values())
        print(f"Broad search found {broad_track_count} tracks")

        # Test study-specific search
        print("🔍 Searching for study tracks...")
        tracks = await manager.find_lofi_for_style(LoFiStyle.STUDY, 180.0, limit=5)

        print(f"Study search found {len(tracks)} tracks")
        for i, track in enumerate(tracks[:3], 1):
            print(f"  {i}. {track.title} by {track.artist} ({track.duration:.1f}s)")

        # Test different styles
        print("🔍 Testing different styles...")
        for style in [LoFiStyle.CHILL, LoFiStyle.AMBIENT, LoFiStyle.RELAXING]:
            style_tracks = await manager.find_lofi_for_style(style, 120.0, limit=3)
            print(f"  {style.value}: {len(style_tracks)} tracks")

        # Cleanup
        await manager.cleanup()
        print("✅ Manager cleanup completed")

        return basic_track_count > 0 or broad_track_count > 0

    except Exception as e:
        print(f"❌ API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_cli():
    """Test basic CLI functionality."""
    print("\nTesting basic CLI...")

    try:
        from cli.main import app

        # Test help command
        print("Testing help command...")
        # This might hang, so let's not actually run it
        print("✅ CLI app created successfully")
        return True

    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Debugging CLI Issues")
    print("=" * 40)

    # Test environment first
    env_ok = test_environment()

    if env_ok and test_imports():
        test_basic_cli()

        # Test API connection
        print("\n" + "=" * 40)
        asyncio.run(test_api_connection())

    print("\n✅ Debug completed")
