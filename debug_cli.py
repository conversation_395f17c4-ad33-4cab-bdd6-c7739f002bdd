#!/usr/bin/env python3
"""
Debug script to test CLI imports and basic functionality.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test all imports step by step."""
    print("Testing imports...")
    
    try:
        print("1. Testing basic imports...")
        import typer
        from rich.console import Console
        print("✅ Typer and Rich imported")
        
        print("2. Testing music sources...")
        from music_sources.base import LoFiStyle, Track, LicenseType
        print("✅ Music sources imported")
        
        print("3. Testing lofi manager...")
        from lofi_manager import LoFiMusicManager
        print("✅ LoFi manager imported")
        
        print("4. Testing video generator...")
        from video_generator import LoFiVideoGenerator
        print("✅ Video generator imported")
        
        print("5. Testing error tracking...")
        from error_tracking import initialize_error_tracking
        print("✅ Error tracking imported")
        
        print("6. Testing upload pipeline...")
        from upload_pipeline import UploadPipeline
        print("✅ Upload pipeline imported")
        
        print("7. Testing CLI main...")
        from cli.main import app
        print("✅ CLI main imported")
        
        print("✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_cli():
    """Test basic CLI functionality."""
    print("\nTesting basic CLI...")
    
    try:
        from cli.main import app
        
        # Test help command
        print("Testing help command...")
        # This might hang, so let's not actually run it
        print("✅ CLI app created successfully")
        return True
        
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Debugging CLI Issues")
    print("=" * 40)
    
    if test_imports():
        test_basic_cli()
    
    print("\n✅ Debug completed")
