#!/usr/bin/env python3
"""
Simple YouTube API setup script that doesn't require heavy imports.
"""

import os
import json
import sys
from pathlib import Path


def print_setup_instructions():
    """Print YouTube API setup instructions."""
    print("🎬 YouTube Data API v3 Setup Instructions")
    print("=" * 50)
    print()
    print("1. Go to Google Cloud Console: https://console.cloud.google.com/")
    print("2. Create a new project or select an existing one")
    print("3. Enable the YouTube Data API v3:")
    print("   - Go to 'APIs & Services' > 'Library'")
    print("   - Search for 'YouTube Data API v3'")
    print("   - Click 'Enable'")
    print()
    print("4. Create a Service Account:")
    print("   - Go to 'APIs & Services' > 'Credentials'")
    print("   - Click 'Create Credentials' > 'Service Account'")
    print("   - Fill in the service account details")
    print("   - Skip role assignment (or assign minimal roles)")
    print("   - Click 'Done'")
    print()
    print("5. Create and download a key:")
    print("   - Click on the created service account")
    print("   - Go to 'Keys' tab")
    print("   - Click 'Add Key' > 'Create new key'")
    print("   - Choose 'JSON' format")
    print("   - Download the key file")
    print()
    print("6. Set up YouTube channel access:")
    print("   - Go to YouTube Studio: https://studio.youtube.com/")
    print("   - Go to Settings > Permissions")
    print("   - Click 'Invite' and add the service account email")
    print("   - Give 'Editor' or 'Manager' permissions")
    print()
    print("7. Set environment variable:")
    print("   export YOUTUBE_SERVICE_ACCOUNT_FILE='/path/to/service-account-key.json'")
    print()


def validate_service_account_file(file_path: str) -> bool:
    """Validate service account JSON file."""
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"❌ File not found: {file_path}")
            return False
        
        with open(path, 'r') as f:
            data = json.load(f)
        
        required_fields = ['type', 'project_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            print(f"❌ Missing required fields: {', '.join(missing_fields)}")
            return False
        
        if data.get('type') != 'service_account':
            print(f"❌ Invalid credential type: {data.get('type')} (expected: service_account)")
            return False
        
        print(f"✅ Service account file is valid")
        print(f"   Project ID: {data.get('project_id')}")
        print(f"   Client Email: {data.get('client_email')}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    required_packages = [
        'google.auth',
        'google.oauth2',
        'googleapiclient',
        'sentry_sdk'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print("❌ Missing required packages:")
        for pkg in missing:
            print(f"   - {pkg}")
        print("\nInstall with:")
        print("pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client sentry-sdk schedule pillow")
        return False
    
    print("✅ All required packages are installed")
    return True


def create_env_template(service_account_file: str):
    """Create .env template with YouTube configuration."""
    project_root = Path(__file__).parent.parent
    env_file = project_root / ".env"
    
    # Read existing .env if it exists
    existing_content = ""
    if env_file.exists():
        with open(env_file, 'r') as f:
            existing_content = f.read()
    
    # YouTube configuration template
    youtube_config = f"""
# YouTube Upload Configuration
YOUTUBE_SERVICE_ACCOUNT_FILE={service_account_file}
YOUTUBE_CHANNEL_ID=your_youtube_channel_id_here

# Scheduling Configuration
TIMEZONE=UTC
DEFAULT_PUBLISH_TIME=22:00

# Slack Notifications (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# GitLab Error Tracking (optional)
SENTRY_DSN=https://<EMAIL>/api/v4/error_tracking/collector/your-project-id

# Database Configuration
DATABASE_PATH=lofi_channel.db
"""
    
    # Check if YouTube config already exists
    if "YOUTUBE_SERVICE_ACCOUNT_FILE" in existing_content:
        print("⚠️ YouTube configuration already exists in .env file")
        response = input("Do you want to update it? (y/N): ").strip().lower()
        if response != 'y':
            return
    
    # Append or create .env file
    with open(env_file, 'a') as f:
        f.write(youtube_config)
    
    print(f"✅ Updated .env file: {env_file}")


def main():
    """Main setup function."""
    print("🚀 Lo-Fi Channel YouTube Setup (Simple)")
    print("=" * 40)
    print()
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first")
        sys.exit(1)
    
    # Check if service account file is already configured
    service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
    
    if not service_account_file:
        print("⚠️ YOUTUBE_SERVICE_ACCOUNT_FILE environment variable not set")
        print()
        print_setup_instructions()
        
        # Ask user for file path
        file_path = input("\nEnter path to your service account JSON file (or press Enter to skip): ").strip()
        if file_path:
            service_account_file = file_path
        else:
            print("Setup incomplete. Please set YOUTUBE_SERVICE_ACCOUNT_FILE and run again.")
            sys.exit(1)
    
    print(f"📁 Using service account file: {service_account_file}")
    print()
    
    # Validate the file
    if not validate_service_account_file(service_account_file):
        print("\n❌ Service account file validation failed")
        print("Please check the file and try again.")
        sys.exit(1)
    
    # Create/update .env file
    create_env_template(service_account_file)
    
    print("\n✅ Basic setup completed!")
    print("\nNext steps:")
    print("1. Set environment variables in your .env file:")
    print(f"   YOUTUBE_SERVICE_ACCOUNT_FILE={service_account_file}")
    print("2. Optionally set SLACK_WEBHOOK_URL for notifications")
    print("3. Optionally set SENTRY_DSN for error tracking")
    print("4. Test upload with: python lofi_cli.py upload video.mp4 chill")
    print("\nTo test the YouTube API connection, run:")
    print("python -c \"import asyncio; from src.youtube.uploader import YouTubeUploader; uploader = YouTubeUploader('{}'); print('✅ Import successful')\"".format(service_account_file))


if __name__ == "__main__":
    main()
