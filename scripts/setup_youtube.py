#!/usr/bin/env python3
"""
Setup script for YouTube API integration.

This script helps users set up YouTube Data API v3 credentials and test the connection.
"""

import os
import json
import sys
from pathlib import Path

# Add src to path for imports
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root / "src"))

from youtube.uploader import YouTubeUploader


def print_setup_instructions():
    """Print YouTube API setup instructions."""
    print("🎬 YouTube Data API v3 Setup Instructions")
    print("=" * 50)
    print()
    print("1. Go to Google Cloud Console: https://console.cloud.google.com/")
    print("2. Create a new project or select an existing one")
    print("3. Enable the YouTube Data API v3:")
    print("   - Go to 'APIs & Services' > 'Library'")
    print("   - Search for 'YouTube Data API v3'")
    print("   - Click 'Enable'")
    print()
    print("4. Create a Service Account:")
    print("   - Go to 'APIs & Services' > 'Credentials'")
    print("   - Click 'Create Credentials' > 'Service Account'")
    print("   - Fill in the service account details")
    print("   - Skip role assignment (or assign minimal roles)")
    print("   - Click 'Done'")
    print()
    print("5. Create and download a key:")
    print("   - Click on the created service account")
    print("   - Go to 'Keys' tab")
    print("   - Click 'Add Key' > 'Create new key'")
    print("   - Choose 'JSON' format")
    print("   - Download the key file")
    print()
    print("6. Set up YouTube channel access:")
    print("   - Go to YouTube Studio: https://studio.youtube.com/")
    print("   - Go to Settings > Permissions")
    print("   - Click 'Invite' and add the service account email")
    print("   - Give 'Editor' or 'Manager' permissions")
    print()
    print("7. Set environment variable:")
    print("   export YOUTUBE_SERVICE_ACCOUNT_FILE='/path/to/service-account-key.json'")
    print()


def validate_service_account_file(file_path: str) -> bool:
    """Validate service account JSON file."""
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"❌ File not found: {file_path}")
            return False
        
        with open(path, 'r') as f:
            data = json.load(f)
        
        required_fields = ['type', 'project_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            print(f"❌ Missing required fields: {', '.join(missing_fields)}")
            return False
        
        if data.get('type') != 'service_account':
            print(f"❌ Invalid credential type: {data.get('type')} (expected: service_account)")
            return False
        
        print(f"✅ Service account file is valid")
        print(f"   Project ID: {data.get('project_id')}")
        print(f"   Client Email: {data.get('client_email')}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False


async def test_youtube_connection(service_account_file: str) -> bool:
    """Test YouTube API connection."""
    try:
        print("🔍 Testing YouTube API connection...")

        uploader = YouTubeUploader(service_account_file)
        
        # Test authentication
        auth_success = await uploader.authenticate()
        if not auth_success:
            print("❌ YouTube authentication failed")
            return False
        
        print("✅ YouTube authentication successful")
        
        # Validate setup
        checks = uploader.validate_setup()
        print("\n📋 Setup Validation:")
        for check, status in checks.items():
            status_icon = "✅" if status else "❌"
            print(f"   {check}: {status_icon}")
        
        all_checks_passed = all(checks.values())
        if all_checks_passed:
            print("\n🎉 YouTube API setup is complete and working!")
        else:
            print("\n⚠️ Some setup checks failed. Please review the issues above.")
        
        return all_checks_passed
        
    except Exception as e:
        print(f"❌ Error testing YouTube connection: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Lo-Fi Channel YouTube Setup")
    print("=" * 40)
    print()
    
    # Check if service account file is already configured
    service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
    
    if not service_account_file:
        print("⚠️ YOUTUBE_SERVICE_ACCOUNT_FILE environment variable not set")
        print()
        print_setup_instructions()
        
        # Ask user for file path
        file_path = input("\nEnter path to your service account JSON file (or press Enter to skip): ").strip()
        if file_path:
            service_account_file = file_path
        else:
            print("Setup incomplete. Please set YOUTUBE_SERVICE_ACCOUNT_FILE and run again.")
            sys.exit(1)
    
    print(f"📁 Using service account file: {service_account_file}")
    print()
    
    # Validate the file
    if not validate_service_account_file(service_account_file):
        print("\n❌ Service account file validation failed")
        print("Please check the file and try again.")
        sys.exit(1)
    
    # Test connection
    print()
    import asyncio
    success = asyncio.run(test_youtube_connection(service_account_file))
    
    if success:
        print("\n✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Set environment variables in your .env file:")
        print(f"   YOUTUBE_SERVICE_ACCOUNT_FILE={service_account_file}")
        print("2. Optionally set SLACK_WEBHOOK_URL for notifications")
        print("3. Optionally set SENTRY_DSN for error tracking")
        print("4. Test upload with: python lofi_cli.py upload video.mp4 chill")
    else:
        print("\n❌ Setup failed. Please review the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
