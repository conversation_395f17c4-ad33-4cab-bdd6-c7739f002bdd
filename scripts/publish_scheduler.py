#!/usr/bin/env python3
"""
Cron job script for publishing scheduled YouTube videos.

This script should be run periodically (e.g., every 5 minutes) via cron to check
for videos scheduled for publication and publish them.

Example crontab entry:
*/5 * * * * /path/to/python /path/to/scripts/publish_scheduler.py

Environment variables required:
- YOUTUBE_SERVICE_ACCOUNT_FILE
- SLACK_WEBHOOK_URL (optional)
- SENTRY_DSN (optional)
- TIMEZONE (optional, defaults to UTC)
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add src to path for imports
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root / "src"))

from dotenv import load_dotenv
from database.manager import DatabaseManager
from youtube.uploader import YouTubeUploader
from notifications.slack import SlackNotifier
from scheduler.scheduler import VideoScheduler
from error_tracking import initialize_error_tracking, capture_exception

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(project_root / "scheduler.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


async def main():
    """Main scheduler function."""
    try:
        # Load environment variables
        load_dotenv(project_root / ".env")
        
        # Initialize error tracking
        sentry_dsn = os.getenv("SENTRY_DSN")
        if sentry_dsn:
            initialize_error_tracking(
                dsn=sentry_dsn,
                environment=os.getenv("ENVIRONMENT", "production")
            )
        
        # Check required environment variables
        service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
        if not service_account_file:
            logger.error("❌ YOUTUBE_SERVICE_ACCOUNT_FILE not set")
            sys.exit(1)
        
        # Initialize components
        db_manager = DatabaseManager(str(project_root / "lofi_channel.db"))
        youtube_uploader = YouTubeUploader(service_account_file)
        
        slack_webhook_url = os.getenv("SLACK_WEBHOOK_URL")
        slack_notifier = SlackNotifier(slack_webhook_url) if slack_webhook_url else None
        
        timezone = os.getenv("TIMEZONE", "UTC")
        scheduler = VideoScheduler(
            db_manager, 
            youtube_uploader, 
            slack_notifier, 
            timezone
        )
        
        # Initialize database and authenticate
        await db_manager.initialize()
        
        auth_success = await youtube_uploader.authenticate()
        if not auth_success:
            logger.error("❌ YouTube authentication failed")
            sys.exit(1)
        
        # Check and publish scheduled videos
        logger.info("🔍 Checking for scheduled videos...")
        published_count = await scheduler.check_and_publish_scheduled_videos()
        
        if published_count > 0:
            logger.info(f"✅ Published {published_count} videos")
        else:
            logger.debug("No videos were scheduled for publication")
        
        # Cleanup
        await db_manager.close()
        
    except Exception as e:
        logger.error(f"❌ Scheduler error: {e}")
        capture_exception(e, context={"component": "publish_scheduler"})
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
