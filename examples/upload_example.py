#!/usr/bin/env python3
"""
Example script demonstrating the YouTube upload pipeline.

This script shows how to:
1. Generate a lo-fi video
2. Upload it to YouTube as a draft
3. Schedule it for publishing
4. Check upload status
"""

import asyncio
import os
import sys
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

# Add src to path for imports
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root / "src"))

from dotenv import load_dotenv
from lofi_manager import LoFiMusicManager
from video_generator import LoFiVideoGenerator
from upload_pipeline import UploadPipeline
from music_sources.base import LoFiStyle
from error_tracking import initialize_error_tracking


async def main():
    """Main example function."""
    print("🎬 Lo-Fi Channel Upload Pipeline Example")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv(project_root / ".env")
    
    # Initialize error tracking
    sentry_dsn = os.getenv("SENTRY_DSN")
    if sentry_dsn:
        initialize_error_tracking(dsn=sentry_dsn)
    
    try:
        # Step 1: Initialize components
        print("\n1️⃣ Initializing components...")
        
        music_manager = LoFiMusicManager()
        video_generator = LoFiVideoGenerator()
        
        # Configure music sources
        freesound_key = os.getenv("FREESOUND_API_KEY")
        if not freesound_key:
            print("❌ FREESOUND_API_KEY not found. Please set it in .env file.")
            return
        
        music_manager.configure_sources(freesound_key)
        
        # Initialize upload pipeline
        service_account_file = os.getenv("YOUTUBE_SERVICE_ACCOUNT_FILE")
        if not service_account_file:
            print("❌ YOUTUBE_SERVICE_ACCOUNT_FILE not found. Please set it in .env file.")
            print("Run: python scripts/setup_youtube.py")
            return
        
        upload_pipeline = UploadPipeline(
            service_account_file=service_account_file,
            slack_webhook_url=os.getenv("SLACK_WEBHOOK_URL"),
            timezone=os.getenv("TIMEZONE", "UTC")
        )
        
        if not await upload_pipeline.initialize():
            print("❌ Failed to initialize upload pipeline")
            return
        
        print("✅ All components initialized successfully")
        
        # Step 2: Generate a video
        print("\n2️⃣ Generating lo-fi video...")
        
        style = LoFiStyle.CHILL
        duration = 120.0  # 2 minutes for demo
        
        # Find a track
        track = await music_manager.get_track_for_video(duration, style)
        if not track:
            print("❌ No suitable track found")
            return
        
        print(f"🎵 Found track: {track.title} by {track.artist}")
        
        # Download audio
        audio_data = await music_manager.download_track_audio(track)
        if not audio_data:
            print("❌ Failed to download audio")
            return
        
        # Generate video
        video_path = await video_generator.generate_video(
            track, audio_data, duration, style
        )
        
        if not video_path:
            print("❌ Failed to generate video")
            return
        
        print(f"✅ Video generated: {video_path}")
        
        # Step 3: Upload to YouTube
        print("\n3️⃣ Uploading to YouTube...")
        
        # Schedule for 1 hour from now (for demo purposes)
        scheduled_time = datetime.now() + timedelta(hours=1)
        
        youtube_video_id = await upload_pipeline.upload_video(
            video_path,
            [track],
            style,
            duration,
            scheduled_publish_time=scheduled_time
        )
        
        if youtube_video_id:
            print(f"✅ Upload successful!")
            print(f"🆔 Video ID: {youtube_video_id}")
            print(f"🔗 URL: https://www.youtube.com/watch?v={youtube_video_id}")
            print(f"📅 Scheduled for: {scheduled_time}")
        else:
            print("❌ Upload failed")
            return
        
        # Step 4: Check status
        print("\n4️⃣ Checking upload status...")
        
        status = await upload_pipeline.get_upload_status()
        print("📊 Pipeline Status:")
        print(f"  Database: {'✅' if status['database_initialized'] else '❌'}")
        print(f"  YouTube: {'✅' if status['youtube_authenticated'] else '❌'}")
        print(f"  Slack: {'✅' if status['slack_enabled'] else '❌'}")
        
        print("\n📈 Videos by Status:")
        for status_name, count in status['videos_by_status'].items():
            print(f"  {status_name}: {count}")
        
        # Step 5: Demonstrate immediate publishing (optional)
        print("\n5️⃣ Optional: Publish immediately?")
        response = input("Do you want to publish the video now instead of waiting? (y/N): ")
        
        if response.lower() == 'y':
            # Get the video ID from database (this is a simplified approach)
            videos = await upload_pipeline.db_manager.get_videos_by_status(
                upload_pipeline.db_manager.UploadStatus.SCHEDULED
            )
            
            if videos:
                latest_video = videos[-1]  # Get the most recent
                success = await upload_pipeline.publish_video_now(latest_video.id)
                
                if success:
                    print("✅ Video published immediately!")
                else:
                    print("❌ Failed to publish video")
            else:
                print("⚠️ No scheduled videos found")
        
        print("\n🎉 Example completed successfully!")
        print("\nNext steps:")
        print("1. Check your YouTube channel for the uploaded video")
        print("2. Set up cron job for automatic publishing:")
        print("   */5 * * * * cd /path/to/lofi-channel && python scripts/publish_scheduler.py")
        print("3. Monitor Slack notifications (if configured)")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        if 'upload_pipeline' in locals():
            await upload_pipeline.cleanup()
        if 'music_manager' in locals():
            await music_manager.cleanup()
        if 'video_generator' in locals():
            video_generator.cleanup_temp_files()


if __name__ == "__main__":
    asyncio.run(main())
